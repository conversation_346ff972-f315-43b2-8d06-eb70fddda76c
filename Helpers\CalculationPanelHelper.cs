// **************************************************************************
// 文件名: CalculationPanelHelper.cs
// 文件职责: 计算面板辅助类，处理复杂的UI交互逻辑
// 功能描述: 提供计算面板的文本同步、事件处理等辅助功能
// 更新日期: 2025-7-21
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 400行（Helper辅助类限制）
// **************************************************************************

using System;
using System.ComponentModel;
using HandyControl.Controls;
using 计算稿纸.ViewModels;

namespace 计算稿纸.Helpers
{
    /// <summary>
    /// 计算面板辅助类
    /// 处理CalculationPanel的复杂交互逻辑
    /// </summary>
    public static class CalculationPanelHelper
    {
        /// <summary>
        /// 处理ViewModel属性变化事件
        /// </summary>
        /// <param name="textEditor">文本编辑器</param>
        /// <param name="viewModel">视图模型</param>
        /// <param name="e">属性变化事件参数</param>
        /// <param name="errorCallback">错误回调</param>
        public static void HandleViewModelPropertyChanged(
            HandyControl.Controls.TextBox? textEditor,
            CalculationViewModel? viewModel,
            PropertyChangedEventArgs e,
            Action<string> errorCallback)
        {
            try
            {
                if (e.PropertyName == nameof(CalculationViewModel.Expression))
                {
                    SynchronizeTextToEditor(textEditor, viewModel, errorCallback);
                }
            }
            catch (Exception ex)
            {
                errorCallback($"处理属性变化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 同步ViewModel的Expression到TextEditor
        /// </summary>
        /// <param name="textEditor">文本编辑器</param>
        /// <param name="viewModel">视图模型</param>
        /// <param name="errorCallback">错误回调</param>
        public static void SynchronizeTextToEditor(
            HandyControl.Controls.TextBox? textEditor,
            CalculationViewModel? viewModel,
            Action<string> errorCallback)
        {
            try
            {
                if (textEditor != null && viewModel != null)
                {
                    if (textEditor.Text != viewModel.Expression)
                    {
                        textEditor.Text = viewModel.Expression;
                    }
                }
            }
            catch (Exception ex)
            {
                errorCallback($"同步文本到编辑器失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 同步TextEditor的文本到ViewModel
        /// </summary>
        /// <param name="textEditor">文本编辑器</param>
        /// <param name="viewModel">视图模型</param>
        /// <param name="errorCallback">错误回调</param>
        public static void SynchronizeTextFromEditor(
            HandyControl.Controls.TextBox? textEditor,
            CalculationViewModel? viewModel,
            Action<string> errorCallback)
        {
            try
            {
                if (textEditor != null && viewModel != null)
                {
                    viewModel.OnTextChanged(textEditor.Text);
                }
            }
            catch (Exception ex)
            {
                errorCallback($"同步文本从编辑器失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理焦点请求
        /// </summary>
        /// <param name="textEditor">文本编辑器</param>
        /// <param name="errorCallback">错误回调</param>
        public static void HandleFocusRequest(HandyControl.Controls.TextBox? textEditor, Action<string> errorCallback)
        {
            try
            {
                textEditor?.Focus();
            }
            catch (Exception ex)
            {
                errorCallback($"设置焦点失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理高度变化
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="newHeight">新高度</param>
        /// <param name="errorCallback">错误回调</param>
        public static void HandleHeightChange(
            System.Windows.FrameworkElement? control, 
            double newHeight, 
            Action<string> errorCallback)
        {
            try
            {
                if (control != null)
                {
                    control.Height = Math.Max(newHeight, 45); // 最小高度45px
                }
            }
            catch (Exception ex)
            {
                errorCallback($"调整高度失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清理ViewModel事件订阅
        /// </summary>
        /// <param name="viewModel">视图模型</param>
        /// <param name="errorOccurredHandler">错误事件处理器</param>
        /// <param name="focusRequestedHandler">焦点请求事件处理器</param>
        /// <param name="heightChangedHandler">高度变化事件处理器</param>
        /// <param name="propertyChangedHandler">属性变化事件处理器</param>
        public static void CleanupViewModelEvents(
            CalculationViewModel? viewModel,
            EventHandler<string>? errorOccurredHandler,
            EventHandler? focusRequestedHandler,
            EventHandler<double>? heightChangedHandler,
            PropertyChangedEventHandler? propertyChangedHandler)
        {
            try
            {
                if (viewModel != null)
                {
                    if (errorOccurredHandler != null)
                        viewModel.ErrorOccurred -= errorOccurredHandler;
                    
                    if (focusRequestedHandler != null)
                        viewModel.FocusRequested -= focusRequestedHandler;
                    
                    if (heightChangedHandler != null)
                        viewModel.HeightChanged -= heightChangedHandler;
                    
                    if (propertyChangedHandler != null)
                        viewModel.PropertyChanged -= propertyChangedHandler;
                    
                    viewModel.Dispose();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理ViewModel事件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 显示错误消息的统一处理
        /// </summary>
        /// <param name="message">错误消息</param>
        public static void ShowError(string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"计算面板错误：{message}");
                // 这里可以添加更友好的错误显示方式
            }
            catch
            {
                // 如果连错误显示都失败，则忽略
            }
        }
    }
}
