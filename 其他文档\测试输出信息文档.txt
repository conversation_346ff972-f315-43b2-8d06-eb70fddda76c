“计算稿纸.exe”(CoreCLR: DefaultDomain): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Private.CoreLib.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\计算稿纸.dll”。已加载符号。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\PresentationFramework.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\WindowsBase.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\System.Xaml.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“c:\program files\microsoft visual studio\2022\professional\common7\ide\commonextensions\microsoft\hotreload\Microsoft.Extensions.DotNetDeltaApplier.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.IO.Pipes.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Linq.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Collections.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Console.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Threading.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.InteropServices.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Threading.Overlapped.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Security.AccessControl.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Security.Principal.Windows.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Security.Claims.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Loader.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\PrivateAssemblies\Runtime\Microsoft.VisualStudio.Debugger.Runtime.NetCoreApp.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\netstandard.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Threading.ThreadPool.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
单步执行: 正在逐过程执行非用户代码“计算稿纸.App..ctor”
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Diagnostics.TraceSource.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Collections.Concurrent.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\System.IO.Packaging.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Text.RegularExpressions.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Private.Uri.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\PresentationCore.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Memory.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\DirectWriteForwarder.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Extensions.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Diagnostics.Debug.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.CompilerServices.VisualC.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\Microsoft.Win32.Registry.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Collections.Specialized.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\Microsoft.Win32.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Collections.NonGeneric.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.ComponentModel.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Threading.Thread.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\System.Configuration.ConfigurationManager.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Xml.ReaderWriter.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Private.Xml.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Net.WebClient.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Text.Encoding.Extensions.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
单步执行: 正在逐过程执行非用户代码“计算稿纸.App.InitializeComponent”
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.ComponentModel.TypeConverter.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\System.Windows.Extensions.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.ComponentModel.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.ObjectModel.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Net.Requests.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Net.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Net.WebHeaderCollection.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\MahApps.Metro.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Resources.ResourceManager.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Diagnostics.Process.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\PresentationFramework.Aero2.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\ControlzEx.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\PresentationFramework-SystemXml.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\System.Windows.Controls.Ribbon.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\HandyControl.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:12.876] [INFO] [System] LoggingService: WPF日志系统初始化完成
[17:52:12.959] [INFO] [System] App: WPF应用程序启动
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\UIAutomationTypes.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Text.Json.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Text.Encodings.Web.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Intrinsics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Numerics.Vectors.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Reflection.Emit.Lightweight.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Reflection.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Reflection.Emit.ILGeneration.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\zh-Hans\PresentationFramework.resources.dll”。模块已生成，不包含符号。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\UIAutomationProvider.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\MahApps.Metro.IconPacks.Material.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“E:\计算稿纸桌面端\计算稿纸\bin\Debug\net8.0-windows\MahApps.Metro.IconPacks.Core.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:13.284] [INFO] [System] MainWindow: 主窗口初始化开始
[17:52:13.287] [TRACE] [ContainerMeasurement] MainWindow: 窗口初始化完成
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\zh-Hans\PresentationCore.resources.dll”。模块已生成，不包含符号。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“c:\program files\microsoft visual studio\2022\professional\common7\ide\commonextensions\microsoft\xamldiagnostics\Core\x64\Microsoft.VisualStudio.DesignTools.WpfTap.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Diagnostics.Tracing.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Threading.Tasks.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Serialization.Json.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Private.DataContractSerialization.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Serialization.Xml.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Serialization.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:13.696] [INFO] [UI] BottomContainerService: 底部容器自动隐藏功能初始化完成
[17:52:13.699] [INFO] [UI] BottomContainerService: 事件处理器设置完成
[17:52:13.701] [INFO] [UI] BottomContainerService: 启动初始隐藏计时器
[17:52:13.702] [INFO] [UI] MainWindow: 底部容器服务初始化完成
[17:52:13.705] [INFO] [UI] MainWindow: 历史记录恢复事件订阅成功
[17:52:13.706] [INFO] [UI] MainWindow: 历史面板HistoryManager连接成功
[17:52:13.709] [INFO] [UI] MainWindow: 全局鼠标监听初始化成功
[17:52:13.711] [INFO] [CharacterWrapping] CalculationPanel: 开始初始化动态字符级换行服务
[17:52:13.713] [TRACE] [ContainerMeasurement] ContainerMeasurementService: DPI缩放比例更新: 1.000
[17:52:13.715] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器测量服务初始化完成
[17:52:13.717] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态字符级换行服务初始化完成
[17:52:13.719] [INFO] [CharacterWrapping] CalculationPanel: 动态字符级换行服务初始化完成
[17:52:13.725] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
面板高度变化，已强制更新布局
[17:52:13.730] [INFO] [UI] CalculationViewModel: 📏 面板高度更新 - 行数:1, 文本高度:45.0px, 总高度:75.0px
[17:52:13.739] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:13.893] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:13.898] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\PresentationUI.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\zh-Hans\PresentationUI.resources.dll”。模块已生成，不包含符号。
[17:52:14.006] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:14.032] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:14.058] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:14.063] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:14.065] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:14.068] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:16.931] [TRACE] [UI] BottomContainerService: 显示底部提示线
[17:52:16.934] [INFO] [UI] BottomContainerService: 底部容器隐藏动画完成
[17:52:16.936] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Drawing.Primitives.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:22.176] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:26.525] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Diagnostics.StackTrace.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:26.727] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:29.365] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:33.826] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.044] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.262] [TRACE] [TextProcessing] CalculationPanel: 文本变化详情 - 来源=用户输入, 光标位置=16
[17:52:34.265] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=16
[17:52:34.268] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:34.272] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'['宽度测量详情: 8.688px
[17:52:34.277] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'长'宽度测量详情: 26.000px
[17:52:34.279] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符']'宽度测量详情: 8.688px
[17:52:34.281] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'2'宽度测量详情: 15.250px
[17:52:34.283] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'*'宽度测量详情: 11.833px
[17:52:34.285] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'宽'宽度测量详情: 26.000px
[17:52:34.287] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'3'宽度测量详情: 15.250px
[17:52:34.289] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'高'宽度测量详情: 26.000px
[17:52:34.292] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'4'宽度测量详情: 15.250px
[17:52:34.294] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:34.296] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:34.298] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:34.300] [TRACE] [TextProcessing] CalculationPanel: 事件循环防护 - TextChanged: 防护=True, 原因=应用换行结果，暂时阻止事件循环
[17:52:34.303] [TRACE] [TextProcessing] CalculationPanel: 文本变化详情 - 来源=用户输入, 光标位置=0
[17:52:34.305] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:34.306] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:34.308] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:34.310] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:34.311] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Data.Common.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Numerics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:34.354] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 CalculateRequiredHeight - 步骤1: 高度计算: 行数=1, 行高=31.20px, 总高度=51.20px
[17:52:34.356] [INFO] [ContainerMeasurement] CalculationPanel: 容器尺寸变化 - 宽度: 45.00px → 51.20px, 高度: 45.00px → 45.00px, 原因: 文本换行导致的高度计算
[17:52:34.379] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
面板高度变化，已强制更新布局
[17:52:34.385] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.387] [INFO] [CharacterWrapping] CalculationPanel: Grid容器高度调整完成: TextBox高度=51.20px, 总面板高度=81.20px
[17:52:34.397] [INFO] [CharacterWrapping] CalculationPanel: 开始初始化动态字符级换行服务
[17:52:34.399] [TRACE] [ContainerMeasurement] ContainerMeasurementService: DPI缩放比例更新: 1.000
[17:52:34.401] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器测量服务初始化完成
[17:52:34.402] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态字符级换行服务初始化完成
[17:52:34.404] [INFO] [CharacterWrapping] CalculationPanel: 动态字符级换行服务初始化完成
面板高度变化，已强制更新布局
[17:52:34.407] [INFO] [UI] CalculationViewModel: 📏 面板高度更新 - 行数:1, 文本高度:45.0px, 总高度:75.0px
[17:52:34.410] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.412] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.471] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.474] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.673] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:34.676] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:36.158] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:36.160] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.074] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.076] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.273] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.275] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.525] [TRACE] [TextProcessing] CalculationPanel: 文本变化详情 - 来源=用户输入, 光标位置=16
[17:52:37.527] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=16
[17:52:37.529] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:37.531] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'【'宽度测量详情: 26.000px
[17:52:37.533] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'长'宽度测量详情: 26.000px
[17:52:37.534] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'】'宽度测量详情: 26.000px
[17:52:37.536] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'3'宽度测量详情: 15.250px
[17:52:37.537] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'*'宽度测量详情: 11.833px
[17:52:37.539] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'宽'宽度测量详情: 26.000px
[17:52:37.540] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'4'宽度测量详情: 15.250px
[17:52:37.542] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'高'宽度测量详情: 26.000px
[17:52:37.544] [TRACE] [CharacterWrapping] CharMeasurementDetail: 字符'2'宽度测量详情: 15.250px
[17:52:37.545] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:37.547] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:37.548] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:37.549] [TRACE] [TextProcessing] CalculationPanel: 事件循环防护 - TextChanged: 防护=True, 原因=应用换行结果，暂时阻止事件循环
[17:52:37.551] [TRACE] [TextProcessing] CalculationPanel: 文本变化详情 - 来源=用户输入, 光标位置=0
[17:52:37.552] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:37.553] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:37.555] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:37.556] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:37.558] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:37.567] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 CalculateRequiredHeight - 步骤1: 高度计算: 行数=1, 行高=31.20px, 总高度=51.20px
[17:52:37.568] [INFO] [ContainerMeasurement] CalculationPanel: 容器尺寸变化 - 宽度: 45.00px → 51.20px, 高度: 45.00px → 45.00px, 原因: 文本换行导致的高度计算
[17:52:37.573] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.575] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
面板高度变化，已强制更新布局
[17:52:37.579] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.581] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.582] [INFO] [CharacterWrapping] CalculationPanel: Grid容器高度调整完成: TextBox高度=51.20px, 总面板高度=81.20px
[17:52:37.585] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.586] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.588] [INFO] [CharacterWrapping] CalculationPanel: 开始初始化动态字符级换行服务
[17:52:37.590] [TRACE] [ContainerMeasurement] ContainerMeasurementService: DPI缩放比例更新: 1.000
[17:52:37.591] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器测量服务初始化完成
[17:52:37.593] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态字符级换行服务初始化完成
[17:52:37.594] [INFO] [CharacterWrapping] CalculationPanel: 动态字符级换行服务初始化完成
面板高度变化，已强制更新布局
[17:52:37.597] [INFO] [UI] CalculationViewModel: 📏 面板高度更新 - 行数:1, 文本高度:45.0px, 总高度:75.0px
[17:52:37.599] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.601] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:37.602] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.105] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.107] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.109] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.195] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.197] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.199] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.441] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.443] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.445] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.473] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.476] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:38.477] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:39.716] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:39.719] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:39.720] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:40.988] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:40.991] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:40.993] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.393] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.396] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.399] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.732] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.734] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:41.736] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.239] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.241] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.243] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.269] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.271] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.273] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.287] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.290] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.292] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.295] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.297] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.299] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.323] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.325] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.328] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.337] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.340] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.342] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.426] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.428] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.430] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.434] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.436] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.437] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.492] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.495] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.498] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.511] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.513] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.514] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.533] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.535] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.536] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.538] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.541] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.543] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.544] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.546] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.547] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.563] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.564] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.566] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.673] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.676] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.678] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.685] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.686] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.688] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.793] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.795] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.797] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.809] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.812] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:42.814] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
“计算稿纸.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Serialization.Formatters.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
[17:52:43.582] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.585] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.587] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.720] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.722] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.724] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.748] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.750] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.752] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.762] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.764] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.766] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.788] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.790] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.792] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.867] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.868] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.870] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.874] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.876] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.878] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.916] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.918] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.919] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.927] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.930] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.931] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.950] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.953] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.954] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.956] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.958] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.960] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.962] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.963] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.965] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.978] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.980] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:43.981] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.800] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.802] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.804] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.850] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.853] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:45.855] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.021] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.023] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.026] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.233] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.236] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.238] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.946] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.948] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.950] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.965] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.969] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.971] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.980] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.982] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.984] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.988] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.990] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:46.991] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.020] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.022] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.023] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.027] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.029] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.030] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.111] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.113] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.115] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.122] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.124] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.126] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.161] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.164] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.166] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.177] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.180] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.181] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.198] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.201] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.202] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.204] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.206] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.207] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.209] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.211] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.212] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.220] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.222] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.223] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.319] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.321] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.323] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.327] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.329] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.330] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.558] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.561] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.563] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.569] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.571] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:47.573] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.164] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.167] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.169] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.308] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.310] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.312] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.332] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.336] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.338] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.346] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.348] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.350] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.373] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.376] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.377] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.427] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.429] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.431] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.436] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.438] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.440] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.480] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.482] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.484] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.511] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.513] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.515] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.531] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.533] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.535] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.537] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.539] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.540] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.542] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.544] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.545] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.561] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.563] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:48.565] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:49.553] [TRACE] [UI] BottomContainerService: 鼠标进入底部区域，停止隐藏计时器
[17:52:49.557] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:49.559] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:49.561] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:49.565] [TRACE] [UI] BottomContainerService: 鼠标离开底部区域，启动延迟隐藏计时器
[17:52:49.634] [TRACE] [UI] BottomContainerService: 鼠标进入底部区域，停止隐藏计时器
[17:52:49.765] [INFO] [UI] BottomContainerService: 底部容器显示动画完成
[17:52:50.395] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.397] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.399] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.516] [INFO] [UI] MainWindow: 历史面板显示动画开始（60FPS）
[17:52:50.531] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.533] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.535] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.547] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.548] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 0.00px → 704.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.551] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 0.00px → 704.00px
[17:52:50.552] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.554] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 0.00px → 704.00px, 原因: 容器尺寸变化
[17:52:50.555] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.557] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 748.00px → 704.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.559] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 748.00px → 704.00px
[17:52:50.561] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.562] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.564] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.565] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.567] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.568] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 748.00px → 704.00px, 原因: 容器尺寸变化
[17:52:50.570] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.571] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 748.00px → 704.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.573] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 748.00px → 704.00px
[17:52:50.574] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.576] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.577] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.579] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.580] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.582] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 748.00px → 704.00px, 原因: 容器尺寸变化
[17:52:50.583] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:745.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.585] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:745.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.586] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:745.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.593] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.594] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 704.00px → 597.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.596] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 704.00px → 597.00px
[17:52:50.598] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.599] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 704.00px → 597.00px, 原因: 容器尺寸变化
[17:52:50.601] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.603] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 704.00px → 597.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.604] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 704.00px → 597.00px
[17:52:50.606] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.608] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.610] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.611] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.612] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.614] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 704.00px → 597.00px, 原因: 容器尺寸变化
[17:52:50.615] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.617] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 704.00px → 597.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.619] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 704.00px → 597.00px
[17:52:50.621] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.622] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.624] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.625] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.627] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.628] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 704.00px → 597.00px, 原因: 容器尺寸变化
[17:52:50.630] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:638.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.632] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:638.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.633] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:638.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.638] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.640] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 597.00px → 545.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.641] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 597.00px → 545.00px
[17:52:50.643] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.644] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 597.00px → 545.00px, 原因: 容器尺寸变化
[17:52:50.646] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.648] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 597.00px → 545.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.649] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 597.00px → 545.00px
[17:52:50.651] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.653] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.655] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.656] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.658] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.660] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 597.00px → 545.00px, 原因: 容器尺寸变化
[17:52:50.661] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.663] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 597.00px → 545.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.665] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 597.00px → 545.00px
[17:52:50.666] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.668] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.669] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.671] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.672] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.674] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 597.00px → 545.00px, 原因: 容器尺寸变化
[17:52:50.675] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:587.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.677] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:587.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.679] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:587.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.683] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.685] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 545.00px → 493.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.686] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 545.00px → 493.00px
[17:52:50.688] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.689] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 545.00px → 493.00px, 原因: 容器尺寸变化
[17:52:50.691] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.693] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 545.00px → 493.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.694] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 545.00px → 493.00px
[17:52:50.696] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.697] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.699] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.700] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.702] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.703] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 545.00px → 493.00px, 原因: 容器尺寸变化
[17:52:50.705] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.706] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 545.00px → 493.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.708] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 545.00px → 493.00px
[17:52:50.710] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.711] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.713] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.715] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.716] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.718] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 545.00px → 493.00px, 原因: 容器尺寸变化
[17:52:50.719] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:535.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.721] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:535.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.722] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:535.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.728] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.730] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 493.00px → 464.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.732] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 493.00px → 464.00px
[17:52:50.734] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.735] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 493.00px → 464.00px, 原因: 容器尺寸变化
[17:52:50.737] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.738] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 493.00px → 464.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.740] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 493.00px → 464.00px
[17:52:50.742] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.744] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.745] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.747] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.749] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.750] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 493.00px → 464.00px, 原因: 容器尺寸变化
[17:52:50.752] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.754] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 493.00px → 464.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.755] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 493.00px → 464.00px
[17:52:50.757] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.759] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.760] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.762] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.764] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.765] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 493.00px → 464.00px, 原因: 容器尺寸变化
[17:52:50.767] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:505.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.769] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:505.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.770] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:505.8x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.775] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.777] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 464.00px → 451.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.779] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 464.00px → 451.00px
[17:52:50.781] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.782] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 464.00px → 451.00px, 原因: 容器尺寸变化
[17:52:50.784] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.786] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 464.00px → 451.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.787] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 464.00px → 451.00px
[17:52:50.789] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.790] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.792] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.793] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.795] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.796] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 464.00px → 451.00px, 原因: 容器尺寸变化
[17:52:50.798] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.799] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 464.00px → 451.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.801] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 464.00px → 451.00px
[17:52:50.803] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.804] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.806] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.807] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.809] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.810] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 464.00px → 451.00px, 原因: 容器尺寸变化
[17:52:50.812] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:493.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.813] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:493.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.815] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:493.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.821] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.822] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 451.00px → 448.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.824] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 451.00px → 448.00px
[17:52:50.826] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:50.827] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 451.00px → 448.00px, 原因: 容器尺寸变化
[17:52:50.829] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.830] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 451.00px → 448.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.832] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 451.00px → 448.00px
[17:52:50.834] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.836] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.837] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.839] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.840] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.842] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 451.00px → 448.00px, 原因: 容器尺寸变化
[17:52:50.843] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.845] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 451.00px → 448.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:50.846] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 451.00px → 448.00px
[17:52:50.848] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:50.849] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:50.851] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:50.853] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:50.854] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:50.856] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 451.00px → 448.00px, 原因: 容器尺寸变化
[17:52:50.858] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:490.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.859] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.861] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.865] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.867] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.868] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:50.879] [TRACE] [UI] BottomContainerService: 鼠标离开底部区域，启动延迟隐藏计时器
[17:52:51.169] [TRACE] [UI] BottomContainerService: 鼠标进入底部区域，停止隐藏计时器
[17:52:51.184] [TRACE] [UI] BottomContainerService: 鼠标离开底部区域，启动延迟隐藏计时器
[17:52:51.401] [INFO] [UI] MainWindow: 历史面板隐藏动画开始（60FPS）
[17:52:51.429] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.433] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.436] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.446] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:490.3x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.449] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.3x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.451] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:490.3x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.469] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.472] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 448.00px → 449.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.475] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 448.00px → 449.00px
[17:52:51.478] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.481] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 448.00px → 449.00px, 原因: 容器尺寸变化
[17:52:51.484] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.487] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 448.00px → 449.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.490] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 448.00px → 449.00px
[17:52:51.514] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.517] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.519] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.520] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.522] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.524] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 448.00px → 449.00px, 原因: 容器尺寸变化
[17:52:51.526] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.528] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 448.00px → 449.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.529] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 448.00px → 449.00px
[17:52:51.531] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.532] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.534] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.535] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.537] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.538] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 448.00px → 449.00px, 原因: 容器尺寸变化
[17:52:51.540] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:491.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.542] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:491.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.544] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:491.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.548] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.550] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 449.00px → 463.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.552] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 449.00px → 463.00px
[17:52:51.554] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.556] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 449.00px → 463.00px, 原因: 容器尺寸变化
[17:52:51.558] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.560] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 449.00px → 463.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.561] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 449.00px → 463.00px
[17:52:51.563] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.564] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.566] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.568] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.570] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.572] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 449.00px → 463.00px, 原因: 容器尺寸变化
[17:52:51.573] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.575] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 449.00px → 463.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.577] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 449.00px → 463.00px
[17:52:51.578] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.580] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.581] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.583] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.585] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.587] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 449.00px → 463.00px, 原因: 容器尺寸变化
[17:52:51.588] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:504.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.590] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:504.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.592] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:504.5x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.596] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.598] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 463.00px → 490.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.600] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 463.00px → 490.00px
[17:52:51.602] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.604] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 463.00px → 490.00px, 原因: 容器尺寸变化
[17:52:51.607] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.609] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 463.00px → 490.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.610] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 463.00px → 490.00px
[17:52:51.612] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.613] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.615] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.617] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.618] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.620] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 463.00px → 490.00px, 原因: 容器尺寸变化
[17:52:51.622] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.623] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 463.00px → 490.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.625] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 463.00px → 490.00px
[17:52:51.626] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.628] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.630] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.631] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.633] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.634] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 463.00px → 490.00px, 原因: 容器尺寸变化
[17:52:51.636] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:532.4x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.637] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:532.4x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.639] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:532.4x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.642] [TRACE] [UI] BottomContainerService: 显示底部提示线
[17:52:51.643] [INFO] [UI] BottomContainerService: 底部容器隐藏动画完成
[17:52:51.648] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.651] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 490.00px → 541.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.652] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 490.00px → 541.00px
[17:52:51.654] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.656] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 490.00px → 541.00px, 原因: 容器尺寸变化
[17:52:51.658] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.660] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 490.00px → 541.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.662] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 490.00px → 541.00px
[17:52:51.664] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.665] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.667] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.669] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.671] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.673] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 490.00px → 541.00px, 原因: 容器尺寸变化
[17:52:51.675] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.677] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 490.00px → 541.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.678] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 490.00px → 541.00px
[17:52:51.680] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.682] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.683] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.685] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.686] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.688] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 490.00px → 541.00px, 原因: 容器尺寸变化
[17:52:51.690] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:583.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.692] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:583.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.693] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:583.1x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.698] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.699] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 541.00px → 622.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.701] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 541.00px → 622.00px
[17:52:51.702] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.704] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 541.00px → 622.00px, 原因: 容器尺寸变化
[17:52:51.706] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.708] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 541.00px → 622.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.709] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 541.00px → 622.00px
[17:52:51.711] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.713] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.714] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.716] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.718] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.719] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 541.00px → 622.00px, 原因: 容器尺寸变化
[17:52:51.721] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.723] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 541.00px → 622.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.725] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 541.00px → 622.00px
[17:52:51.726] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.728] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.729] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.731] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.733] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.734] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 541.00px → 622.00px, 原因: 容器尺寸变化
[17:52:51.736] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:663.6x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.738] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:663.6x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.740] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:663.6x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.746] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.748] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 622.00px → 739.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.750] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 622.00px → 739.00px
[17:52:51.751] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.754] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 622.00px → 739.00px, 原因: 容器尺寸变化
[17:52:51.756] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.758] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 622.00px → 739.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.760] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 622.00px → 739.00px
[17:52:51.762] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.763] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.765] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.767] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.768] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.770] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 622.00px → 739.00px, 原因: 容器尺寸变化
[17:52:51.772] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.774] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 622.00px → 739.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.775] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 622.00px → 739.00px
[17:52:51.777] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.779] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.780] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.782] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.783] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.785] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 622.00px → 739.00px, 原因: 容器尺寸变化
[17:52:51.788] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:780.7x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.790] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:780.7x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.791] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:780.7x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.796] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.798] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 739.00px → 748.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.800] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 739.00px → 748.00px
[17:52:51.802] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=0
[17:52:51.804] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 739.00px → 748.00px, 原因: 容器尺寸变化
[17:52:51.805] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.808] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 739.00px → 748.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.810] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 739.00px → 748.00px
[17:52:51.811] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.813] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.815] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.817] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.818] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.820] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 739.00px → 748.00px, 原因: 容器尺寸变化
[17:52:51.822] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.824] [INFO] [ContainerMeasurement] ContainerMeasurementService: 容器尺寸变化 - 宽度: 739.00px → 748.00px, 高度: 45.00px → 45.00px, 原因: 用户拖拽窗口
[17:52:51.825] [INFO] [ContainerMeasurement] CalculationPanel: 容器宽度变化触发重新换行: 739.00px → 748.00px
[17:52:51.827] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 开始动态换行处理: 文本长度=14
[17:52:51.828] [TRACE] [ContainerMeasurement] ContainerMeasurementService: 容器宽度计算完成
[17:52:51.830] [TRACE] [CharacterWrapping] DynamicCharacterWrappingService: 🔄 ApplyCharacterLevelWrapping - 步骤3: 字符级换行完成: 测量字符数=14
[17:52:51.832] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 换行算法性能 - 文本长度=14, 行数=1, 测量字符数=14
[17:52:51.834] [INFO] [CharacterWrapping] DynamicCharacterWrappingService: 动态换行处理完成: 原长度=14, 新长度=14, 行数=1
[17:52:51.835] [INFO] [ContainerMeasurement] ContainerWidthMonitor: 容器宽度变化: 739.00px → 748.00px, 原因: 容器尺寸变化
[17:52:51.837] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.839] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:51.841] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.397] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.400] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.402] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.413] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.415] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.417] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.546] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.548] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.549] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.554] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.556] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.558] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.595] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.598] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.601] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.613] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.616] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.618] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.650] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.653] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.655] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.658] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.661] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.664] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.666] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.668] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.669] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.683] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.684] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:52.686] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.664] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.666] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.668] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.674] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.676] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.678] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.852] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.854] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.856] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.937] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.940] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:53.942] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.105] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.108] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.110] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.228] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.230] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.232] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.843] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.845] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.846] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.858] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.860] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:54.863] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.027] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.029] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.031] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.034] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.036] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.038] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.106] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.108] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.110] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.131] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.133] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.135] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.147] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.149] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.151] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.152] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.154] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.155] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.157] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.159] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.160] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.174] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.176] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.178] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.950] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.952] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:55.954] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.160] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.163] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.165] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.560] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.562] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.564] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.576] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.578] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.580] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.601] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.604] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.606] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.646] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.648] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.650] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.654] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.656] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.658] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.693] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.696] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.698] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.714] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.716] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.718] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.732] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.734] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.735] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.737] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.738] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.739] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.742] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.743] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.745] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.760] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.762] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:56.763] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.826] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.828] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.830] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.853] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.856] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:52:59.858] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:00.630] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:00.632] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:00.633] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.409] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.411] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.413] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.720] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.723] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:01.725] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:02.711] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,45.0), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:02.712] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
[17:53:02.714] [INFO] [UI] CalculationPanel: 🎯 等号与结果区域位置监控 - 位置:(5.0,51.2), 大小:790.0x30.0, 可见:True, TextEditor高度:45.0
