// **************************************************************************
// 文件名: FormulaEngine.cs
// 文件职责: 公式计算引擎，负责解析和计算数学表达式
// 功能描述: 提供数学表达式的解析、计算和结果格式化功能
// 更新日期: 2025-7-21
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 500行（Service类限制）
// **************************************************************************

using System;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;

namespace 计算稿纸.Core
{
    /// <summary>
    /// 公式计算引擎
    /// 负责解析和计算数学表达式
    /// </summary>
    public class FormulaEngine : IDisposable
    {
        private bool _disposed;

        /// <summary>
        /// 构造函数
        /// </summary>
        public FormulaEngine()
        {
        }

        /// <summary>
        /// 计算数学表达式
        /// </summary>
        /// <param name="expression">数学表达式</param>
        /// <returns>计算结果</returns>
        public object? Calculate(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return null;

            try
            {
                // 预处理表达式
                string processedExpression = PreprocessExpression(expression);
                
                if (string.IsNullOrWhiteSpace(processedExpression))
                    return null;

                // 使用DataTable.Compute进行计算
                var table = new DataTable();
                var result = table.Compute(processedExpression, null);
                
                // 格式化结果
                return FormatResult(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算表达式失败: {expression}, 错误: {ex.Message}");
                return "错误";
            }
        }

        /// <summary>
        /// 预处理表达式
        /// </summary>
        /// <param name="expression">原始表达式</param>
        /// <returns>处理后的表达式</returns>
        private string PreprocessExpression(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return string.Empty;

            try
            {
                // 移除多余的空白字符
                string processed = expression.Trim();
                
                // 移除中文字符和备注（保留数字、运算符、括号）
                processed = Regex.Replace(processed, @"[^\d+\-*/().×÷]", "");
                
                // 替换中文运算符
                processed = processed.Replace("×", "*");
                processed = processed.Replace("÷", "/");
                
                // 处理连续的运算符
                processed = Regex.Replace(processed, @"[+\-*/]{2,}", "+");
                
                // 移除开头和结尾的运算符
                processed = processed.Trim('+', '-', '*', '/', '.');
                
                return processed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预处理表达式失败: {expression}, 错误: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 格式化计算结果
        /// </summary>
        /// <param name="result">计算结果</param>
        /// <returns>格式化后的结果字符串</returns>
        private string FormatResult(object? result)
        {
            if (result == null || result == DBNull.Value)
                return string.Empty;

            try
            {
                // 转换为数值类型
                if (double.TryParse(result.ToString(), out double numericResult))
                {
                    // 如果是整数，显示为整数（支持万亿级数字）
                    if (Math.Abs(numericResult - Math.Round(numericResult)) < 1e-10)
                    {
                        // 对于大数字使用千分位分隔符
                        if (Math.Abs(numericResult) >= 1000)
                        {
                            return Math.Round(numericResult).ToString("N0", CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            return Math.Round(numericResult).ToString("F0", CultureInfo.InvariantCulture);
                        }
                    }
                    else
                    {
                        // 小数保留适当位数，大数字也使用千分位分隔符
                        if (Math.Abs(numericResult) >= 1000)
                        {
                            return numericResult.ToString("N2", CultureInfo.InvariantCulture).TrimEnd('0').TrimEnd('.');
                        }
                        else
                        {
                            return numericResult.ToString("F2", CultureInfo.InvariantCulture).TrimEnd('0').TrimEnd('.');
                        }
                    }
                }

                return result.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"格式化结果失败: {result}, 错误: {ex.Message}");
                return result?.ToString() ?? string.Empty;
            }
        }

        /// <summary>
        /// 验证表达式是否有效
        /// </summary>
        /// <param name="expression">数学表达式</param>
        /// <returns>是否有效</returns>
        public bool IsValidExpression(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return false;

            try
            {
                string processed = PreprocessExpression(expression);
                if (string.IsNullOrWhiteSpace(processed))
                    return false;

                // 尝试计算以验证有效性
                var table = new DataTable();
                var result = table.Compute(processed, null);
                return result != null && result != DBNull.Value;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取表达式的计算步骤（用于调试）
        /// </summary>
        /// <param name="expression">数学表达式</param>
        /// <returns>计算步骤信息</returns>
        public string GetCalculationSteps(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return "表达式为空";

            try
            {
                string original = expression;
                string processed = PreprocessExpression(expression);
                var result = Calculate(expression);

                return $"原始: {original}\n处理后: {processed}\n结果: {result}";
            }
            catch (Exception ex)
            {
                return $"计算步骤获取失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 清理表达式中的无效字符
        /// </summary>
        /// <param name="expression">原始表达式</param>
        /// <returns>清理后的表达式</returns>
        public string CleanExpression(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return string.Empty;

            try
            {
                // 保留数字、基本运算符、括号和小数点
                string cleaned = Regex.Replace(expression, @"[^\d+\-*/().×÷\s]", "");
                
                // 替换中文运算符
                cleaned = cleaned.Replace("×", "*");
                cleaned = cleaned.Replace("÷", "/");
                
                // 规范化空格
                cleaned = Regex.Replace(cleaned, @"\s+", " ").Trim();
                
                return cleaned;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理表达式失败: {expression}, 错误: {ex.Message}");
                return expression;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // 清理托管资源
                // FormulaEngine没有需要特别清理的资源
                _disposed = true;
            }
        }
    }
}
