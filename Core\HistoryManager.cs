// **************************************************************************
// 文件名: HistoryManager.cs
// 文件职责: 计算历史记录管理器，负责计算历史记录的管理
// 功能描述: 提供计算历史记录的获取、排序、删除等功能
// 更新日期: 2025-7-21
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 500行（Service类限制）
// **************************************************************************

using System;
using System.Collections.Generic;
using System.Linq;

namespace 计算稿纸.Core
{
    /// <summary>
    /// 计算历史记录项
    /// </summary>
    public class HistoryItem
    {
        /// <summary>
        /// 计算表达式
        /// </summary>
        public string Expression { get; set; } = string.Empty;

        /// <summary>
        /// 计算结果
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// 计算时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 文件名（用于历史记录恢复功能）
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public HistoryItem()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 带参数构造函数
        /// </summary>
        /// <param name="expression">计算表达式</param>
        /// <param name="result">计算结果</param>
        public HistoryItem(string expression, string result)
        {
            Expression = expression;
            Result = result;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 带文件名的构造函数（用于历史记录恢复功能）
        /// </summary>
        /// <param name="expression">计算表达式</param>
        /// <param name="result">计算结果</param>
        /// <param name="fileName">文件名</param>
        public HistoryItem(string expression, string result, string fileName)
        {
            Expression = expression;
            Result = result;
            FileName = fileName;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 计算历史记录管理器
    /// </summary>
    public class HistoryManager : IDisposable
    {
        #region 事件定义

        /// <summary>
        /// 历史记录更新事件（复刻源项目通知机制）
        /// </summary>
        public event EventHandler? HistoryUpdated;

        #endregion

        #region 私有字段

        private readonly List<HistoryItem> _historyItems;
        private readonly int _maxHistoryCount = 1000; // 最大历史记录数量
        private bool _disposed;

        #endregion

        #region 构造函数和公共方法

        /// <summary>
        /// 构造函数
        /// </summary>
        public HistoryManager()
        {
            _historyItems = new List<HistoryItem>();
            // 移除示例数据，进入实际测试阶段
        }

        /// <summary>
        /// 添加历史记录
        /// </summary>
        /// <param name="expression">计算表达式</param>
        /// <param name="result">计算结果</param>
        public void AddHistory(string expression, string result)
        {
            if (string.IsNullOrWhiteSpace(expression) || string.IsNullOrWhiteSpace(result))
            {
                Services.LoggingService.LogWarning(Services.LogCategory.HistoryManagement, "HistoryManager",
                    "添加历史记录失败：表达式或结果为空", new { expression, result }, Services.OperationType.SAVE_OPERATION);
                return;
            }

            var historyItem = new HistoryItem(expression, result);
            _historyItems.Insert(0, historyItem); // 插入到开头

            // 限制历史记录数量
            if (_historyItems.Count > _maxHistoryCount)
            {
                _historyItems.RemoveAt(_historyItems.Count - 1);
                Services.LoggingService.LogInfo(Services.LogCategory.HistoryManagement, "HistoryManager",
                    "历史记录数量超限，移除最旧记录", new { maxCount = _maxHistoryCount }, Services.OperationType.SAVE_OPERATION);
            }

            // 记录保存操作日志
            Services.LoggingService.LogInfo(Services.LogCategory.HistoryManagement, "HistoryManager",
                "✅ 成功添加历史记录到内存列表", new {
                    expression = expression.Length > 50 ? expression.Substring(0, 50) + "..." : expression,
                    result = result.Length > 20 ? result.Substring(0, 20) + "..." : result,
                    totalCount = _historyItems.Count
                }, Services.OperationType.SAVE_OPERATION);

            // 触发历史记录更新事件（复刻源项目通知机制）
            OnHistoryUpdated();
        }

        /// <summary>
        /// 添加历史记录（包含文件名）
        /// </summary>
        /// <param name="expression">计算表达式</param>
        /// <param name="result">计算结果</param>
        /// <param name="fileName">文件名</param>
        public void AddHistory(string expression, string result, string fileName)
        {
            if (string.IsNullOrWhiteSpace(expression) || string.IsNullOrWhiteSpace(result))
            {
                Services.LoggingService.LogWarning(Services.LogCategory.HistoryManagement, "HistoryManager",
                    "添加历史记录失败：表达式或结果为空", new { expression, result, fileName }, Services.OperationType.SAVE_OPERATION);
                return;
            }

            var historyItem = new HistoryItem(expression, result, fileName);
            _historyItems.Insert(0, historyItem); // 插入到开头

            // 限制历史记录数量
            if (_historyItems.Count > _maxHistoryCount)
            {
                _historyItems.RemoveAt(_historyItems.Count - 1);
                Services.LoggingService.LogInfo(Services.LogCategory.HistoryManagement, "HistoryManager",
                    "历史记录数量超限，移除最旧记录", new { maxCount = _maxHistoryCount }, Services.OperationType.SAVE_OPERATION);
            }

            // 记录保存操作日志（包含文件名）
            Services.LoggingService.LogInfo(Services.LogCategory.HistoryManagement, "HistoryManager",
                "✅ 成功添加历史记录到内存列表（含文件名）", new {
                    expression = expression.Length > 50 ? expression.Substring(0, 50) + "..." : expression,
                    result = result.Length > 20 ? result.Substring(0, 20) + "..." : result,
                    fileName,
                    totalCount = _historyItems.Count
                }, Services.OperationType.SAVE_OPERATION);

            // 触发历史记录更新事件（复刻源项目通知机制）
            OnHistoryUpdated();
        }

        /// <summary>
        /// 获取所有历史记录
        /// </summary>
        /// <returns>历史记录列表</returns>
        public List<HistoryItem> GetAllHistory()
        {
            return new List<HistoryItem>(_historyItems);
        }

        /// <summary>
        /// 清空历史记录
        /// </summary>
        public void ClearHistory()
        {
            _historyItems.Clear();
        }

        /// <summary>
        /// 删除指定历史记录
        /// </summary>
        /// <param name="item">要删除的历史记录项</param>
        /// <returns>删除是否成功</returns>
        public bool RemoveHistory(HistoryItem item)
        {
            return _historyItems.Remove(item);
        }

        /// <summary>
        /// 搜索历史记录
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <returns>匹配的历史记录列表</returns>
        public List<HistoryItem> SearchHistory(string keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return GetAllHistory();
            }

            var searchTerm = keyword.ToLower();
            return _historyItems.Where(h =>
                h.Expression.ToLower().Contains(searchTerm) ||
                h.Result.ToLower().Contains(searchTerm)
            ).ToList();
        }

        /// <summary>
        /// 获取历史记录数量
        /// </summary>
        /// <returns>历史记录数量</returns>
        public int GetHistoryCount()
        {
            return _historyItems.Count;
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发历史记录更新事件
        /// </summary>
        protected virtual void OnHistoryUpdated()
        {
            HistoryUpdated?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 加载示例数据（临时方法）
        /// </summary>
        private void LoadSampleData()
        {
            // 添加一些示例历史记录（包含FileName用于恢复功能测试）
            _historyItems.Add(new HistoryItem("A-F轴_墙皮开打面积计算", "2507.18", "sample_001"));
            _historyItems.Add(new HistoryItem("大风风光", "250718-1017", "sample_002"));
            _historyItems.Add(new HistoryItem("都会发生给面而刷刷", "250718-...", "sample_003"));
            _historyItems.Add(new HistoryItem("测试一", "250721-1014", "sample_004"));
            _historyItems.Add(new HistoryItem("测试3", "250721-1019", "sample_005"));
            _historyItems.Add(new HistoryItem("A-D轴墙体计算体积", "250721-...", "sample_006"));
            _historyItems.Add(new HistoryItem("1-8轴墙体体积计算", "250721-1...", "sample_007"));
            _historyItems.Add(new HistoryItem("A-F轴墙体面积计算", "250721-1...", "sample_008"));
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _historyItems?.Clear();
                _disposed = true;
            }
        }

        #endregion
    }
}
