/*
 * 文件名: BottomContainerBehavior.cs
 * 职责: 底部容器的WPF行为类，处理附加行为和样式
 * 功能描述:
 *   - 底部容器的圆角样式设置
 *   - Transform动画支持
 *   - 容器初始化行为
 * 迁移源: 被迁移winform计算稿纸项目原稿\UI\Core\BottomContainerManager.cs
 * 更新日期: 2025-7-25
 * 版本编号: 1.0.0
 * 作者: Prꪮᥴꫀડડ柚子
 * 代码行数限制: ≤ 300行（行为类限制）
 */

using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Xaml.Behaviors;
using 计算稿纸.Services;

namespace 计算稿纸.Behaviors
{
    /// <summary>
    /// 底部容器行为类 - 处理底部容器的附加行为
    /// </summary>
    public class BottomContainerBehavior : Behavior<Border>
    {
        #region 依赖属性

        /// <summary>
        /// 底部容器服务依赖属性
        /// </summary>
        public static readonly DependencyProperty BottomContainerServiceProperty =
            DependencyProperty.Register(
                nameof(BottomContainerService),
                typeof(BottomContainerService),
                typeof(BottomContainerBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 底部容器服务
        /// </summary>
        public BottomContainerService BottomContainerService
        {
            get => (BottomContainerService)GetValue(BottomContainerServiceProperty);
            set => SetValue(BottomContainerServiceProperty, value);
        }

        /// <summary>
        /// 圆角半径依赖属性
        /// </summary>
        public static readonly DependencyProperty CornerRadiusProperty =
            DependencyProperty.Register(
                nameof(CornerRadius),
                typeof(CornerRadius),
                typeof(BottomContainerBehavior),
                new PropertyMetadata(new CornerRadius(8), OnCornerRadiusChanged));

        /// <summary>
        /// 圆角半径
        /// </summary>
        public CornerRadius CornerRadius
        {
            get => (CornerRadius)GetValue(CornerRadiusProperty);
            set => SetValue(CornerRadiusProperty, value);
        }

        /// <summary>
        /// 是否启用动画Transform依赖属性
        /// </summary>
        public static readonly DependencyProperty EnableAnimationTransformProperty =
            DependencyProperty.Register(
                nameof(EnableAnimationTransform),
                typeof(bool),
                typeof(BottomContainerBehavior),
                new PropertyMetadata(true, OnEnableAnimationTransformChanged));

        /// <summary>
        /// 是否启用动画Transform
        /// </summary>
        public bool EnableAnimationTransform
        {
            get => (bool)GetValue(EnableAnimationTransformProperty);
            set => SetValue(EnableAnimationTransformProperty, value);
        }

        #endregion

        #region 行为生命周期

        /// <summary>
        /// 附加到目标元素时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            try
            {
                InitializeBottomContainer();
                LoggingService.LogInfo(LogCategory.UI, "BottomContainerBehavior",
                    "行为附加完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "行为附加失败", ex);
            }
        }

        /// <summary>
        /// 从目标元素分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            try
            {
                CleanupBottomContainer();
                LoggingService.LogInfo(LogCategory.UI, "BottomContainerBehavior",
                    "行为分离完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "行为分离失败", ex);
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化底部容器
        /// </summary>
        private void InitializeBottomContainer()
        {
            if (AssociatedObject == null) return;

            try
            {
                // 设置圆角
                ApplyCornerRadius();
                
                // 设置动画Transform
                if (EnableAnimationTransform)
                {
                    SetupAnimationTransform();
                }

                // 设置初始样式
                SetupInitialStyle();

                LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                    "底部容器初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "初始化底部容器失败", ex);
            }
        }

        /// <summary>
        /// 应用圆角设置
        /// </summary>
        private void ApplyCornerRadius()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.CornerRadius = CornerRadius;
            }
        }

        /// <summary>
        /// 设置动画Transform
        /// </summary>
        private void SetupAnimationTransform()
        {
            if (AssociatedObject == null) return;

            try
            {
                // 创建TranslateTransform用于动画
                var translateTransform = new TranslateTransform();
                AssociatedObject.RenderTransform = translateTransform;

                LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                    "动画Transform设置完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "设置动画Transform失败", ex);
            }
        }

        /// <summary>
        /// 设置初始样式
        /// </summary>
        private void SetupInitialStyle()
        {
            if (AssociatedObject == null) return;

            try
            {
                // 设置初始可见性
                AssociatedObject.Visibility = Visibility.Visible;

                // 设置Z-Index确保在顶层
                Panel.SetZIndex(AssociatedObject, 1000);

                LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                    "初始样式设置完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "设置初始样式失败", ex);
            }
        }

        /// <summary>
        /// 清理底部容器
        /// </summary>
        private void CleanupBottomContainer()
        {
            try
            {
                // 清理Transform
                if (AssociatedObject != null)
                {
                    AssociatedObject.RenderTransform = null;
                }

                LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                    "底部容器清理完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "清理底部容器失败", ex);
            }
        }

        #endregion

        #region 依赖属性变化处理

        /// <summary>
        /// 圆角半径变化处理
        /// </summary>
        private static void OnCornerRadiusChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is BottomContainerBehavior behavior && behavior.AssociatedObject != null)
            {
                try
                {
                    behavior.ApplyCornerRadius();
                    LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                        $"圆角半径更新: {e.NewValue}");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                        "更新圆角半径失败", ex);
                }
            }
        }

        /// <summary>
        /// 动画Transform启用状态变化处理
        /// </summary>
        private static void OnEnableAnimationTransformChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is BottomContainerBehavior behavior && behavior.AssociatedObject != null)
            {
                try
                {
                    bool enableTransform = (bool)e.NewValue;
                    if (enableTransform)
                    {
                        behavior.SetupAnimationTransform();
                    }
                    else
                    {
                        behavior.AssociatedObject.RenderTransform = null;
                    }

                    LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                        $"动画Transform状态更新: {enableTransform}");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                        "更新动画Transform状态失败", ex);
                }
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 手动触发初始化（用于运行时动态添加）
        /// </summary>
        public void ManualInitialize()
        {
            try
            {
                InitializeBottomContainer();
                LoggingService.LogInfo(LogCategory.UI, "BottomContainerBehavior",
                    "手动初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "手动初始化失败", ex);
            }
        }

        /// <summary>
        /// 重置Transform到初始状态
        /// </summary>
        public void ResetTransform()
        {
            try
            {
                if (AssociatedObject?.RenderTransform is TranslateTransform transform)
                {
                    transform.X = 0;
                    transform.Y = 0;
                }

                LoggingService.LogDebug(LogCategory.UI, "BottomContainerBehavior",
                    "Transform重置完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "重置Transform失败", ex);
            }
        }

        /// <summary>
        /// 获取当前Transform状态
        /// </summary>
        public (double X, double Y) GetTransformState()
        {
            try
            {
                if (AssociatedObject?.RenderTransform is TranslateTransform transform)
                {
                    return (transform.X, transform.Y);
                }
                return (0, 0);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "BottomContainerBehavior",
                    "获取Transform状态失败", ex);
                return (0, 0);
            }
        }

        #endregion
    }
}
