// **************************************************************************
// 文件名: CalculationSheet.cs
// 文件职责: 计算稿纸数据模型，定义稿纸的完整数据结构
// 功能描述: 支持稿纸内容的序列化与反序列化，包含公式、结果、备注等信息
// 更新日期: 2025-7-25
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 300行（Model类限制）
// **************************************************************************

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;

namespace 计算稿纸.Models
{
    /// <summary>
    /// 计算稿纸数据模型
    /// 包含完整的稿纸信息，支持保存和加载
    /// </summary>
    public class CalculationSheet : INotifyPropertyChanged
    {
        #region 私有字段

        private string _id = Guid.NewGuid().ToString();
        private string _name = string.Empty;
        private string _description = string.Empty;
        private DateTime _createdTime = DateTime.Now;
        private DateTime _lastModifiedTime = DateTime.Now;
        private string _version = "1.0.0";
        private ObservableCollection<CalculationItem> _items = new ObservableCollection<CalculationItem>();

        #endregion

        #region 公共属性

        /// <summary>
        /// 稿纸唯一标识符
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 稿纸名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 稿纸描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime
        {
            get => _createdTime;
            set => SetProperty(ref _createdTime, value);
        }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime
        {
            get => _lastModifiedTime;
            set => SetProperty(ref _lastModifiedTime, value);
        }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version
        {
            get => _version;
            set => SetProperty(ref _version, value);
        }

        /// <summary>
        /// 计算项列表
        /// </summary>
        public ObservableCollection<CalculationItem> Items
        {
            get => _items;
            set => SetProperty(ref _items, value);
        }

        /// <summary>
        /// 计算项数量
        /// </summary>
        [JsonIgnore]
        public int ItemCount => Items?.Count ?? 0;

        /// <summary>
        /// 是否有内容
        /// </summary>
        [JsonIgnore]
        public bool HasContent => Items?.Any(item => !string.IsNullOrWhiteSpace(item.Formula)) ?? false;

        /// <summary>
        /// 有效计算项数量
        /// </summary>
        [JsonIgnore]
        public int ValidItemCount => Items?.Count(item => item.IsValid) ?? 0;

        #endregion

        #region INotifyPropertyChanged实现

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 设置属性值并触发属性变更通知
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">属性字段</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否发生了变更</returns>
        protected bool SetProperty<T>(ref T field, T value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            
            // 更新修改时间
            if (propertyName != nameof(LastModifiedTime))
            {
                LastModifiedTime = DateTime.Now;
            }
            
            return true;
        }

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public CalculationSheet()
        {
            Items = new ObservableCollection<CalculationItem>();
        }

        /// <summary>
        /// 带名称的构造函数
        /// </summary>
        /// <param name="name">稿纸名称</param>
        public CalculationSheet(string name) : this()
        {
            Name = name;
        }

        /// <summary>
        /// 完整构造函数
        /// </summary>
        /// <param name="name">稿纸名称</param>
        /// <param name="description">稿纸描述</param>
        public CalculationSheet(string name, string description) : this(name)
        {
            Description = description;
        }

        #endregion

        #region 计算项管理方法

        /// <summary>
        /// 添加计算项
        /// </summary>
        /// <param name="item">计算项</param>
        public void AddItem(CalculationItem item)
        {
            if (item != null)
            {
                item.Order = Items.Count;
                Items.Add(item);
                LastModifiedTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 移除计算项
        /// </summary>
        /// <param name="item">计算项</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveItem(CalculationItem item)
        {
            if (item != null && Items.Remove(item))
            {
                // 重新排序
                ReorderItems();
                LastModifiedTime = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清空所有计算项
        /// </summary>
        public void ClearItems()
        {
            Items.Clear();
            LastModifiedTime = DateTime.Now;
        }

        /// <summary>
        /// 重新排序计算项
        /// </summary>
        private void ReorderItems()
        {
            for (int i = 0; i < Items.Count; i++)
            {
                Items[i].Order = i;
            }
        }

        /// <summary>
        /// 获取有效的计算项
        /// </summary>
        /// <returns>有效计算项列表</returns>
        public List<CalculationItem> GetValidItems()
        {
            return Items.Where(item => item.IsValid).ToList();
        }

        /// <summary>
        /// 获取有备注的计算项
        /// </summary>
        /// <returns>有备注的计算项列表</returns>
        public List<CalculationItem> GetItemsWithNotes()
        {
            return Items.Where(item => item.HasNote).ToList();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 复制稿纸
        /// </summary>
        /// <returns>新的稿纸实例</returns>
        public CalculationSheet Clone()
        {
            var clonedSheet = new CalculationSheet
            {
                Id = Guid.NewGuid().ToString(), // 生成新的ID
                Name = $"{Name} - 副本",
                Description = Description,
                CreatedTime = DateTime.Now,
                LastModifiedTime = DateTime.Now,
                Version = Version
            };

            // 复制所有计算项
            foreach (var item in Items)
            {
                clonedSheet.AddItem(item.Clone());
            }

            return clonedSheet;
        }

        /// <summary>
        /// 获取稿纸摘要信息
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"{Name} - {ItemCount}项计算，{ValidItemCount}项有效";
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return GetSummary();
        }

        /// <summary>
        /// 重写Equals方法
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object? obj)
        {
            if (obj is CalculationSheet other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// 重写GetHashCode方法
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        #endregion
    }
}
