// **************************************************************************
// 文件名: CalculationItem.cs
// 文件职责: 计算项数据模型，支持公式、结果、备注等信息
// 功能描述: WPF版本的计算项数据结构，支持数据绑定和序列化
// 更新日期: 2025-7-25
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 300行（Model类限制）
// **************************************************************************

using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace 计算稿纸.Models
{
    /// <summary>
    /// 计算项数据模型
    /// 支持公式计算、结果显示、备注信息等功能
    /// </summary>
    public class CalculationItem : INotifyPropertyChanged
    {
        #region 私有字段

        private string _id = Guid.NewGuid().ToString();
        private string _formula = string.Empty;
        private string _result = string.Empty;
        private bool _isValid = false;
        private string _errorMessage = string.Empty;
        private string _note = string.Empty;
        private DateTime _createdTime = DateTime.Now;
        private int _order = 0;

        #endregion

        #region 公共属性

        /// <summary>
        /// 计算项唯一标识符
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 公式表达式
        /// </summary>
        public string Formula
        {
            get => _formula;
            set => SetProperty(ref _formula, value);
        }

        /// <summary>
        /// 表达式（Formula的别名，用于兼容性）
        /// </summary>
        [JsonIgnore]
        public string Expression
        {
            get => _formula;
            set => SetProperty(ref _formula, value);
        }

        /// <summary>
        /// 计算结果
        /// </summary>
        public string Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        /// <summary>
        /// 是否有效（计算成功）
        /// </summary>
        public bool IsValid
        {
            get => _isValid;
            set => SetProperty(ref _isValid, value);
        }

        /// <summary>
        /// 错误信息（如果计算失败）
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Note
        {
            get => _note;
            set => SetProperty(ref _note, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime
        {
            get => _createdTime;
            set => SetProperty(ref _createdTime, value);
        }

        /// <summary>
        /// 在稿纸中的顺序
        /// </summary>
        public int Order
        {
            get => _order;
            set => SetProperty(ref _order, value);
        }

        /// <summary>
        /// 是否有备注
        /// </summary>
        [JsonIgnore]
        public bool HasNote => !string.IsNullOrWhiteSpace(Note);

        #endregion

        #region INotifyPropertyChanged实现

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 设置属性值并触发属性变更通知
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">属性字段</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否发生了变更</returns>
        protected bool SetProperty<T>(ref T field, T value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public CalculationItem()
        {
        }

        /// <summary>
        /// 带参数构造函数
        /// </summary>
        /// <param name="formula">公式表达式</param>
        /// <param name="result">计算结果</param>
        public CalculationItem(string formula, string result)
        {
            Formula = formula;
            Result = result;
            IsValid = !string.IsNullOrWhiteSpace(result);
        }

        /// <summary>
        /// 完整构造函数
        /// </summary>
        /// <param name="formula">公式表达式</param>
        /// <param name="result">计算结果</param>
        /// <param name="note">备注信息</param>
        public CalculationItem(string formula, string result, string note)
        {
            Formula = formula;
            Result = result;
            Note = note;
            IsValid = !string.IsNullOrWhiteSpace(result);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 清空计算项内容
        /// </summary>
        public void Clear()
        {
            Formula = string.Empty;
            Result = string.Empty;
            Note = string.Empty;
            ErrorMessage = string.Empty;
            IsValid = false;
        }

        /// <summary>
        /// 复制计算项内容
        /// </summary>
        /// <returns>新的计算项实例</returns>
        public CalculationItem Clone()
        {
            return new CalculationItem
            {
                Id = Guid.NewGuid().ToString(), // 生成新的ID
                Formula = Formula,
                Result = Result,
                Note = Note,
                ErrorMessage = ErrorMessage,
                IsValid = IsValid,
                CreatedTime = DateTime.Now, // 使用当前时间
                Order = Order
            };
        }

        /// <summary>
        /// 获取显示文本
        /// </summary>
        /// <returns>用于显示的文本</returns>
        public string GetDisplayText()
        {
            if (!string.IsNullOrWhiteSpace(Formula))
            {
                return $"{Formula} = {Result}";
            }
            return string.Empty;
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return GetDisplayText();
        }

        /// <summary>
        /// 重写Equals方法
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object? obj)
        {
            if (obj is CalculationItem other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// 重写GetHashCode方法
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        #endregion
    }
}
