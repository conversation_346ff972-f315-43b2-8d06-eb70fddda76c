/*
 * 文件名: HistoryAnimationBehavior.cs
 * 职责: 历史面板的WPF动画行为类，处理显示隐藏动画
 * 功能描述:
 *   - 历史面板宽度展开/收缩动画
 *   - 右对齐位置计算和调整
 *   - 平滑动画效果管理
 * 迁移源: 被迁移winform计算稿纸项目原稿\UI\Controls\HistoryPanel.cs
 * 更新日期: 2025-7-25
 * 版本编号: 1.0.0
 * 作者: Prꪮᥴꫀડડ柚子
 * 代码行数限制: ≤ 400行（行为类限制）
 */

using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using Microsoft.Xaml.Behaviors;
using 计算稿纸.Services;

namespace 计算稿纸.Behaviors
{
    /// <summary>
    /// 历史面板动画行为类 - 处理历史面板的显示隐藏动画
    /// </summary>
    public class HistoryAnimationBehavior : Behavior<FrameworkElement>
    {
        #region 依赖属性

        /// <summary>
        /// 目标宽度依赖属性
        /// </summary>
        public static readonly DependencyProperty TargetWidthProperty =
            DependencyProperty.Register(
                nameof(TargetWidth),
                typeof(double),
                typeof(HistoryAnimationBehavior),
                new PropertyMetadata(300.0));

        /// <summary>
        /// 目标宽度
        /// </summary>
        public double TargetWidth
        {
            get => (double)GetValue(TargetWidthProperty);
            set => SetValue(TargetWidthProperty, value);
        }

        /// <summary>
        /// 动画持续时间依赖属性
        /// </summary>
        public static readonly DependencyProperty AnimationDurationProperty =
            DependencyProperty.Register(
                nameof(AnimationDuration),
                typeof(Duration),
                typeof(HistoryAnimationBehavior),
                new PropertyMetadata(new Duration(TimeSpan.FromMilliseconds(300))));

        /// <summary>
        /// 动画持续时间
        /// </summary>
        public Duration AnimationDuration
        {
            get => (Duration)GetValue(AnimationDurationProperty);
            set => SetValue(AnimationDurationProperty, value);
        }

        /// <summary>
        /// 是否启用右对齐依赖属性
        /// </summary>
        public static readonly DependencyProperty EnableRightAlignProperty =
            DependencyProperty.Register(
                nameof(EnableRightAlign),
                typeof(bool),
                typeof(HistoryAnimationBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否启用右对齐
        /// </summary>
        public bool EnableRightAlign
        {
            get => (bool)GetValue(EnableRightAlignProperty);
            set => SetValue(EnableRightAlignProperty, value);
        }

        #endregion

        #region 私有字段

        private Storyboard? _showStoryboard;
        private Storyboard? _hideStoryboard;
        private bool _isAnimating = false;

        #endregion

        #region 行为生命周期

        /// <summary>
        /// 附加到目标元素时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            try
            {
                InitializeAnimations();
                LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                    "历史面板动画行为附加完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "行为附加失败", ex);
            }
        }

        /// <summary>
        /// 从目标元素分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            try
            {
                CleanupAnimations();
                LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                    "历史面板动画行为分离完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "行为分离失败", ex);
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化动画
        /// </summary>
        private void InitializeAnimations()
        {
            if (AssociatedObject == null) return;

            try
            {
                // 设置初始状态
                AssociatedObject.Width = 0;
                AssociatedObject.Visibility = Visibility.Collapsed;

                // 创建显示动画
                CreateShowAnimation();
                
                // 创建隐藏动画
                CreateHideAnimation();

                LoggingService.LogDebug(LogCategory.UI, "HistoryAnimationBehavior",
                    "动画初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "初始化动画失败", ex);
            }
        }

        /// <summary>
        /// 创建显示动画
        /// </summary>
        private void CreateShowAnimation()
        {
            if (AssociatedObject == null) return;

            try
            {
                _showStoryboard = new Storyboard();

                // 创建宽度动画
                var widthAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = TargetWidth,
                    Duration = AnimationDuration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                Storyboard.SetTarget(widthAnimation, AssociatedObject);
                Storyboard.SetTargetProperty(widthAnimation, new PropertyPath(FrameworkElement.WidthProperty));
                
                _showStoryboard.Children.Add(widthAnimation);
                
                _showStoryboard.Completed += (s, e) =>
                {
                    _isAnimating = false;
                    if (EnableRightAlign)
                    {
                        UpdateRightAlignment();
                    }
                    LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                        "显示动画完成");
                };

                LoggingService.LogDebug(LogCategory.UI, "HistoryAnimationBehavior",
                    "显示动画创建完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "创建显示动画失败", ex);
            }
        }

        /// <summary>
        /// 创建隐藏动画
        /// </summary>
        private void CreateHideAnimation()
        {
            if (AssociatedObject == null) return;

            try
            {
                _hideStoryboard = new Storyboard();

                // 创建宽度动画
                var widthAnimation = new DoubleAnimation
                {
                    From = TargetWidth,
                    To = 0,
                    Duration = AnimationDuration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
                };

                Storyboard.SetTarget(widthAnimation, AssociatedObject);
                Storyboard.SetTargetProperty(widthAnimation, new PropertyPath(FrameworkElement.WidthProperty));
                
                _hideStoryboard.Children.Add(widthAnimation);
                
                _hideStoryboard.Completed += (s, e) =>
                {
                    AssociatedObject.Visibility = Visibility.Collapsed;
                    _isAnimating = false;
                    LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                        "隐藏动画完成");
                };

                LoggingService.LogDebug(LogCategory.UI, "HistoryAnimationBehavior",
                    "隐藏动画创建完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "创建隐藏动画失败", ex);
            }
        }

        /// <summary>
        /// 清理动画资源
        /// </summary>
        private void CleanupAnimations()
        {
            try
            {
                _showStoryboard?.Stop();
                _hideStoryboard?.Stop();
                _showStoryboard = null;
                _hideStoryboard = null;

                LoggingService.LogDebug(LogCategory.UI, "HistoryAnimationBehavior",
                    "动画资源清理完成");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "清理动画资源失败", ex);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示面板
        /// </summary>
        public void ShowPanel()
        {
            try
            {
                if (_isAnimating || AssociatedObject == null) return;

                _isAnimating = true;
                AssociatedObject.Visibility = Visibility.Visible;

                if (EnableRightAlign)
                {
                    UpdateRightAlignment();
                }

                _showStoryboard?.Begin();

                LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                    "开始显示动画");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "显示面板失败", ex);
                _isAnimating = false;
            }
        }

        /// <summary>
        /// 隐藏面板
        /// </summary>
        public void HidePanel()
        {
            try
            {
                if (_isAnimating || AssociatedObject == null) return;

                _isAnimating = true;
                _hideStoryboard?.Begin();

                LoggingService.LogInfo(LogCategory.UI, "HistoryAnimationBehavior",
                    "开始隐藏动画");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "隐藏面板失败", ex);
                _isAnimating = false;
            }
        }

        /// <summary>
        /// 更新右对齐位置
        /// </summary>
        private void UpdateRightAlignment()
        {
            try
            {
                if (AssociatedObject?.Parent is Panel parentPanel)
                {
                    // 计算右对齐位置
                    double rightMargin = parentPanel.ActualWidth - AssociatedObject.ActualWidth;
                    if (rightMargin > 0)
                    {
                        AssociatedObject.Margin = new Thickness(rightMargin, 
                            AssociatedObject.Margin.Top, 0, AssociatedObject.Margin.Bottom);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(LogCategory.UI, "HistoryAnimationBehavior",
                    "更新右对齐位置失败", ex);
            }
        }

        /// <summary>
        /// 获取动画状态
        /// </summary>
        public bool IsAnimating => _isAnimating;

        #endregion
    }
}
